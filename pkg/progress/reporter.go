package progress

import (
	"fmt"
	"os"
	"strings"
	"sync"
	"time"
)

// Info holds all the data for a single progress update.
type Info struct {
	BytesDone   int64
	BytesTotal  int64
	Speed       float64 // MB/s
	AvgSpeed    float64 // MB/s
	ElapsedTime float64 // seconds
	ETA         string
	PercentDone float64
	Operation   string
	Stage       string // For multi-stage operations
	Summary     string // For the final report
}

// Reporter handles consistent progress reporting.
type Reporter struct {
	startTime  time.Time
	operation  string
	stage      string // Current stage of a multi-stage operation
	mu         sync.Mutex
	lastBytes  int64
	lastUpdate time.Time
	UpdateChan chan<- Info
}

// NewReporter creates a new progress reporter.
func NewReporter(operation string, update<PERSON>han chan<- Info) *Reporter {
	return &Reporter{
		startTime:  time.Now(),
		operation:  operation,
		stage:      operation, // Default stage is the operation itself
		UpdateChan: updateChan,
		lastUpdate: time.Now(),
	}
}

// SetStage sets the current stage for a multi-stage operation.
func (r *Reporter) SetStage(stage string) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.stage = stage
	// Reset start time for accurate per-stage metrics if needed, but for now, we use overall time.
}

// Update calculates and reports progress.
func (r *Reporter) Update(bytesDone, bytesTotal int64) {
	r.mu.Lock()
	defer r.mu.Unlock()

	now := time.Now()
	elapsed := now.Sub(r.startTime).Seconds()
	if elapsed == 0 {
		elapsed = 1 // prevent division by zero
	}

	// For terminal output, rate limit to once per second
	if r.UpdateChan == nil && now.Sub(r.lastUpdate) < time.Second {
		return
	}
	r.lastUpdate = now

	avgSpeed := float64(bytesDone) / elapsed / 1024 / 1024

	var etaStr string
	if avgSpeed > 0 && bytesTotal > 0 {
		remainingBytes := float64(bytesTotal - bytesDone)
		etaSeconds := remainingBytes / (avgSpeed * 1024 * 1024)
		etaDuration := time.Duration(etaSeconds * float64(time.Second)).Round(time.Second)
		if etaSeconds < 86400*7 {
			etaStr = fmt.Sprintf("ETA: %s", etaDuration)
		}
	}

	percentDone := 0.0
	if bytesTotal > 0 {
		percentDone = float64(bytesDone) * 100.0 / float64(bytesTotal)
	}

	info := Info{
		BytesDone:   bytesDone,
		BytesTotal:  bytesTotal,
		AvgSpeed:    avgSpeed,
		ElapsedTime: elapsed,
		ETA:         etaStr,
		PercentDone: percentDone,
		Operation:   r.operation,
		Stage:       r.stage, // Use the stored stage
	}

	// If a channel is provided, send the update. Otherwise, print to terminal.
	if r.UpdateChan != nil {
		r.UpdateChan <- info
	} else {
		r.printToTerminal(info)
	}
}

// printToTerminal displays progress on the terminal (for CLI mode).
func (r *Reporter) printToTerminal(info Info) {
	barWidth := 40
	filled := int(info.PercentDone * float64(barWidth) / 100.0)
	if filled < 0 {
		filled = 0
	}
	if filled > barWidth {
		filled = barWidth
	}
	progressBar := "[" + strings.Repeat("█", filled) + strings.Repeat("░", barWidth-filled) + "]"

	totalStr := HumanizeBytes(uint64(info.BytesTotal))
	if info.BytesTotal <= 0 {
		totalStr = "???"
	}

	// Make stage capitalized and clean for display
	displayStage := strings.Title(strings.ToLower(info.Stage))

	progressLine := fmt.Sprintf("\r%s: %s %.1f%% %s / %s (%.2f MB/s) %s",
		displayStage, progressBar, info.PercentDone,
		HumanizeBytes(uint64(info.BytesDone)), totalStr,
		info.AvgSpeed, info.ETA)

	// Clear the line and print the progress
	fmt.Fprintf(os.Stderr, "\033[2K%s", progressLine)
}

// Finish sends the final report.
func (r *Reporter) Finish(bytesDone int64) {
	// Ensure the final progress update shows 100%
	// In some cases, the total bytes might be slightly off, so we force 100%
	// by setting bytesDone equal to bytesTotal in the final update.
	if r.UpdateChan != nil {
		// For the UI, send a final 100% update before the summary.
		r.Update(bytesDone, bytesDone)
	}

	duration := time.Since(r.startTime).Round(time.Second)
	if duration < time.Second {
		duration = time.Second // Avoid display of "0s"
	}
	avgSpeed := 0.0
	if duration.Seconds() > 0 {
		avgSpeed = float64(bytesDone) / duration.Seconds() / (1024 * 1024)
	}

	// Capitalize stage for the summary
	displayStage := strings.Title(strings.ToLower(r.stage))
	summary := fmt.Sprintf("%s completed.\n  - Total Data: %s\n  - Duration: %s\n  - Average Speed: %.2f MB/s",
		displayStage, HumanizeBytes(uint64(bytesDone)), duration, avgSpeed)

	if r.UpdateChan != nil {
		// In UI mode, we don't send a summary here. The copier logic sends a final one.
		// We just close our channel to signal this stage is done.
		// A small delay might be needed for the last update to render.
		time.Sleep(50 * time.Millisecond)
	} else {
		// In CLI mode, print the final summary.
		fmt.Fprint(os.Stderr, "\n") // Newline after progress bar
		fmt.Fprintln(os.Stderr, summary)
	}
}

// HumanizeBytes converts bytes to a human-readable string.
func HumanizeBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := int64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %ciB", float64(b)/float64(div), "KMGTPE"[exp])
}
